{"name": "uems", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~8.2.14", "@angular/cdk": "~8.2.3", "@angular/common": "~8.2.14", "@angular/compiler": "~8.2.14", "@angular/core": "~8.2.14", "@angular/forms": "~8.2.14", "@angular/material": "^8.2.3", "@angular/platform-browser": "~8.2.14", "@angular/platform-browser-dynamic": "~8.2.14", "@angular/router": "~8.2.14", "@auth0/angular-jwt": "^3.0.1", "@material-extended/mde": "^2.3.0", "@mdi/font": "^4.7.95", "@ng-bootstrap/ng-bootstrap": "^4.2.1", "ajv": "6.4.0", "angular-calendar": "^0.27.8", "angular-user-idle": "^2.2.5", "arrive": "2.4.1", "bn-ng-idle": "^1.0.1", "bootstrap": "4.1.0", "bootstrap-material-design": "4.1.1", "bootstrap-notify": "3.1.3", "chart.js": "^2.9.3", "chartist": "0.11.0", "classlist.js": "^1.1.20150312", "core-js": "^2.6.9", "date-fns": "^1.30.1", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "exceljs": "^1.12.0", "export-to-csv": "^0.2.1", "express": "4.16.3", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "googleapis": "28.1.0", "hammerjs": "^2.0.8", "html2canvas": "^1.0.0-rc.5", "intl": "^1.2.5", "jquery": "3.2.1", "jspdf": "^1.5.3", "luxon": "^1.16.0", "mat-select-filter": "^2.3.6", "mat-video": "^2.8.1", "material-design-icons": "^3.0.1", "material-design-icons-iconfont": "^6.1.0", "moment": "2.22.1", "ng-multiselect-dropdown": "^0.3.4", "ng2-charts": "^2.3.0", "ngx-color-picker": "^8.2.0", "ngx-cookie-service": "^2.2.0", "ngx-mat-select-search": "^1.8.0", "ngx-material-timepicker": "^3.3.1", "ngx-toastr": "^10.0.4", "node-sass": "^4.12.0", "option": "^0.2.4", "perfect-scrollbar": "1.1.0", "popper.js": "1.14.3", "rxjs": "~6.4.0", "sweetalert2": "^9.5.4", "tslib": "^1.10.0", "web-animations-js": "^2.3.2", "xlsx": "^0.15.4", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.803.20", "@angular/cli": "~8.3.20", "@angular/compiler-cli": "~8.2.14", "@angular/language-service": "~8.2.14", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "~8.9.4", "codelyzer": "^5.0.0", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.1.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "protractor": "~5.4.0", "ts-node": "~7.0.0", "tslint": "~5.15.0", "typescript": "^3.5.3"}}